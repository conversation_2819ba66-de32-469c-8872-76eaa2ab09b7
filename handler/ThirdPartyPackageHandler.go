package handler

import (
	"encoding/json"
	"fmt"
	"github.com/go-playground/validator/v10"
	"mime/multipart"
	"net/http"
	"patch-central-repo/common"
	"patch-central-repo/logger"
	thirdparty2 "patch-central-repo/model/thirdparty"
	"patch-central-repo/rest"
	"patch-central-repo/service/thirdparty"
	"strings"
)

type ThirdPartyPackageHandler struct {
	Service *thirdparty.ThirdPartyPackageService
}

func NewThirdPartyPackageHandler() *ThirdPartyPackageHandler {
	return &ThirdPartyPackageHandler{Service: thirdparty.NewThirdPartyPackageService()}
}

func (handler ThirdPartyPackageHandler) GetAllThirdPartyPackage(w http.ResponseWriter, r *http.Request) {
	var searchFilter rest.SearchFilter
	searchFilter, err := rest.ConvertJsonToSearchFilter(w, r, searchFilter)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
	pageResponse, err := handler.Service.GetAllPackages(searchFilter)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAllThirdPartyPackage]", err.Error())
		}
		return
	}
	jsonData, _ := common.RestToJson(w, pageResponse)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetAllThirdPartyPackage]", err.Error())
	}
}

func (handler ThirdPartyPackageHandler) CreateThirdPartyPackage(w http.ResponseWriter, r *http.Request) {
	var thirdPartyPackage thirdparty2.ThirdPartyPackageRest
	thirdPartyPackage, err := ConvertJsonToThirdPartyPackage(w, r, thirdPartyPackage)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
	id, customErr := handler.Service.Create(thirdPartyPackage)
	if customErr.Message != "" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while creating third party package : "+customErr.Message, http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[CreateThirdPartyPackage]", err.Error())
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{
		Id:      id,
		Success: true,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[CreateThirdPartyPackage]", err.Error())
	}
}

func (handler ThirdPartyPackageHandler) UpdateThirdPartyPackage(w http.ResponseWriter, r *http.Request) {
	var thirdPartyPackage thirdparty2.ThirdPartyPackageRest
	thirdPartyPackage, err := ConvertJsonToThirdPartyPackage(w, r, thirdPartyPackage)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	pkg := handler.Service.Repository.GetByUUId(thirdPartyPackage.Uuid)
	if pkg.Id > 0 {
		_, err = handler.Service.Repository.PermanentDeleteById(pkg.Id)
		if err != nil {
			logger.ServiceLogger.Error("[UpdateThirdPartyPackage]", err.Error())
		}
	}

	id, customErr := handler.Service.Create(thirdPartyPackage)
	if customErr.Message != "" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while creating third party package : "+customErr.Message, http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[UpdateThirdPartyPackage]", err.Error())
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{
		Id:      id,
		Success: true,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[UpdateThirdPartyPackage]", err.Error())
	}
}

func (packageHandler ThirdPartyPackageHandler) UploadThirdPartyPackageXml(w http.ResponseWriter, r *http.Request) {
	err := r.ParseMultipartForm(1024 << 20)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error : "+err.Error(), http.StatusInternalServerError))
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[UploadThirdPartyPackageXml]", err.Error())
		}
		return
	} // 1024 MB limit
	file, handler, err := r.FormFile("file")
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error : "+err.Error(), http.StatusInternalServerError))
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[UploadThirdPartyPackageXml]", err.Error())
		}
		return
	}
	defer func(file multipart.File) {
		err := file.Close()
		if err != nil {

		}
	}(file)

	handler.Filename = strings.ReplaceAll(handler.Filename, " ", "_")
	_, err = common.UploadPatchXml(handler, err, file)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error : "+err.Error(), http.StatusInternalServerError))
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[UploadThirdPartyPackageXml]", err.Error())
		}
		return
	}
	_, err = fmt.Fprintf(w, "success")
	if err != nil {
		logger.ServiceLogger.Error("[UploadThirdPartyPackageXml]", err.Error())
		return
	}
}

func ConvertJsonToThirdPartyPackage(w http.ResponseWriter, r *http.Request, thirdPartyPackage thirdparty2.ThirdPartyPackageRest) (thirdparty2.ThirdPartyPackageRest, error) {
	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &thirdPartyPackage)
	v := validator.New()
	err = v.Struct(thirdPartyPackage)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			jsonData, _ := common.RestToJson(w, common.Error(fmt.Sprintf("Validation error on field %s, Expected values : %s", err.Field(), err.Param()), http.StatusInternalServerError))
			w.WriteHeader(http.StatusInternalServerError)
			_, err1 := fmt.Fprintf(w, jsonData)
			if err1 != nil {
				logger.ServiceLogger.Error("[ConvertJsonToThirdPartyPackage]", err1.Error())
				return thirdparty2.ThirdPartyPackageRest{}, err1
			}
			break
		}
	}
	return thirdPartyPackage, err
}
