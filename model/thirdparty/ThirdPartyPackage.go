package thirdparty

import (
	"patch-central-repo/common"
	"patch-central-repo/model"
	"patch-central-repo/rest"
)

type ThirdPartyPackage struct {
	model.BaseEntityModel
	Description      string                       `bun:"type:varchar(500)" json:"description"`
	Version          string                       `bun:"type:varchar(100)" json:"version"`
	Os               common.OsType                `json:"os"`
	Arch             common.OsArchitecture        `json:"arch"`
	LanguageCode     string                       `bun:"type:varchar(50)" json:"languageCode"`
	PkgFileData      []model.FileData             `json:"pkgFileData"`
	LatestPackageUrl string                       `bun:"type:varchar(1000)" json:"latestPackageUrl"`
	Publisher        string                       `bun:"type:varchar(100)" json:"publisher"`
	SupportUrl       string                       `bun:"type:varchar(500)" json:"supportUrl"`
	ReleaseNote      string                       `bun:"type:varchar(100)" json:"releaseNote"`
	ReleaseDate      int64                        `bun:"type:varchar(500)" json:"releaseDate"`
	Application      common.ThirdPartyApplication `json:"application"`
	CveDetails       []CveDetails                 `json:"cveDetails"`
	Uuid             string                       `bun:"type:varchar(500)" json:"uuid"`
	UpdateId         string                       `bun:"type:varchar(500)" json:"updateId"`
	OsVersion        string                       `bun:"type:varchar(50)" json:"osVersion"`
}

type CveDetails struct {
	CveId       string `json:"cveId"`
	Impact      string `json:"impact"`
	Description string `json:"description"`
	References  string `json:"references"`
}

type ThirdPartyPackageRest struct {
	rest.BaseEntityRest
	Description      string           `json:"description"`
	Version          string           `json:"version"`
	Os               string           `json:"os"`
	Arch             string           `json:"arch"`
	LanguageCode     string           `json:"languageCode"`
	PkgFileData      []model.FileData `json:"pkgFileData"`
	LatestPackageUrl string           `json:"latestPackageUrl"`
	Publisher        string           `json:"publisher"`
	SupportUrl       string           `json:"supportUrl"`
	ReleaseNote      string           `json:"releaseNote"`
	ReleaseDate      int64            `json:"releaseDate"`
	Application      string           `json:"application"`
	CveDetails       []CveDetails     `json:"cveDetails"`
	Uuid             string           `json:"uuid"`
	UpdateId         string           `json:"updateId"`
}
