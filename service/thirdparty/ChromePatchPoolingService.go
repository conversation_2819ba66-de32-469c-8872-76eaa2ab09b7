package thirdparty

import (
	"bytes"
	"encoding/xml"
	"fmt"
	"io"
	"net/http"
	"patch-central-repo/common"
	"patch-central-repo/logger"
	"patch-central-repo/model"
	"patch-central-repo/model/thirdparty"
	"strings"
	"time"
)

type ChromePatchPoolingService struct {
}

type chromeXmlResponse struct {
	XMLName     xml.Name    `xml:"response"`
	UpdateCheck updateCheck `xml:"app>updatecheck"`
}

type updateCheck struct {
	Status   string   `xml:"status,attr"`
	Manifest manifest `xml:"manifest"`
}

type manifest struct {
	Version string `xml:"version,attr"`
}

const (
	apiUrl = "https://tools.google.com/service/update2"
)

var (
	appidByOs = map[string]string{
		"WINDOWS": "{8A69D345-D564-463C-AFF1-A69D9E530F96}",
		//"MAC":     "com.google.Chrome",
	}

	versionsByOs = map[string][]string{
		"WINDOWS": {"7", "8", "10", "11"},
	}

	archByOs = map[string][]string{
		"WINDOWS": {"x64"},
		//"MAC":     {"arm64"},
	}

	chromeMetaData = map[string]interface{}{
		"uuid": "3c31ba85-f743-496c-a175-c676285007fc",
		"templateFileNameMap": map[string]interface{}{
			"x64": "GOOGLE_CHROME_X64.xml",
			"x86": "GOOGLE_CHROME_X32.xml",
		},
	}
)

func NewChromePatchPoolingService() *ChromePatchPoolingService {
	return &ChromePatchPoolingService{}
}

func (service ChromePatchPoolingService) ExecuteSync() {
	fetchAllData()
}

type patchScanRequest struct {
	Platform     string
	OsArch       string
	LanguageCode int
	OsVersion    string
}

func fetchAllData() {
	for platform := range appidByOs {
		archs := archByOs[platform]
		osVersions := versionsByOs[platform]
		for _, osVersion := range osVersions {
			for _, arch := range archs {
				request := patchScanRequest{
					Platform:     platform,
					OsArch:       arch,
					LanguageCode: 1033,
					OsVersion:    osVersion,
				}
				err := fetchLatestPatchData(request)
				if err != nil {
					logger.ServiceLogger.Error("Error while fetching chrome data: ", err)
				}
			}
		}
	}
}

func fetchLatestPatchData(request patchScanRequest) error {
	logger.ServiceLogger.Info(fmt.Sprintf("Fetching chrome data for os %s ,  arch %s\n", request.Platform, request.OsArch))

	// Prepare XML request body
	osplatform := "win"
	osType := common.Windows

	xmlRequestBody := fmt.Sprintf(`<?xml version='1.0' encoding='UTF-8'?>
<request protocol='3.0' version='1.3.23.9' shell_version='1.3.21.103' ismachine='1'
    sessionid='{%s}' installsource='ondemandcheckforupdate'
    requestid='{CD7523AD-A40D-49F4-AEEF-8C114B804658}' dedup='cr'>
    <os platform='%s' version='%s' arch='%s'/>
    <app appid='%s' lang='en'>
        <updatecheck/>
    </app>
</request>`, "example-uuid", osplatform, request.OsVersion, request.OsArch, appidByOs[request.Platform])

	resp, err := http.Post(apiUrl, "application/xml", bytes.NewBuffer([]byte(xmlRequestBody)))
	if err != nil {
		return fmt.Errorf("HTTP POST error: %v", err)
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}(resp.Body)

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("read response error: %v", err)
	}

	var xmlResponse chromeXmlResponse
	err = xml.Unmarshal(body, &xmlResponse)
	if err != nil {
		return fmt.Errorf("XML to JSON parsing error: %v", err)
	}

	if xmlResponse.UpdateCheck.Status == "ok" {
		logger.ServiceLogger.Info(fmt.Sprintf("Found valid data for chrome for os %s , arch %s\n", request.Platform, request.OsArch))

		// Extract required data
		version := xmlResponse.UpdateCheck.Manifest.Version
		downloadUrl := ""
		fileName := ""
		if osType == common.MacOS {
			downloadUrl = "https://dl.google.com/dl/chrome/mac/universal/stable/gcea/googlechrome.dmg"
			fileName = "googlechrome.dmg"
		} else {
			if request.OsArch == "x86" {
				downloadUrl = "https://dl.google.com/edgedl/chrome/install/GoogleChromeStandaloneEnterprise.msi"
				fileName = "GoogleChromeStandaloneEnterprise.msi"
			} else {
				downloadUrl = "https://dl.google.com/edgedl/chrome/install/GoogleChromeStandaloneEnterprise64.msi"
				fileName = "GoogleChromeStandaloneEnterprise64.msi"
			}
		}

		arch := common.X64
		if strings.Contains(request.OsArch, "32") || strings.Contains(request.OsArch, "86") {
			arch = common.X86
		}

		if osType == common.MacOS {
			arch = common.All
		}
		uuid := strings.Join([]string{common.CHROME.String(), version, arch.String(), osType.String(), request.OsVersion}, "-")
		var pkg thirdparty.ThirdPartyPackage
		modelPkg := NewThirdPartyPackageService().Repository.GetByUUId(uuid) // Adjust toString method accordingly
		if modelPkg.Id > 0 {
			pkg = modelPkg
		} else {
			logger.ServiceLogger.Info("No existing chrome package found")
			existingPkg, _ := NewThirdPartyPackageService().Repository.GetPkgByOsVersionPlatformOsArchApplication(request.OsVersion, int(arch), int(osType), int(common.CHROME))
			if existingPkg.Id > 0 {
				logger.ServiceLogger.Info("Deleting existing chrome package")
				DeleteXmlForWindows(existingPkg.Uuid)
				_, _ = NewThirdPartyPackageService().Repository.DeletePatch(existingPkg)
			}
			pkg = thirdparty.ThirdPartyPackage{}
		}

		// Get file size from download URL using HEAD request
		fileSize := common.GetFileSizeFromUrl(downloadUrl)

		headers := common.GetHeadersFromUrl(downloadUrl)

		date := headers.Get("Last-Modified")
		if date != "" {
			t, err := time.Parse(time.RFC1123, date)
			if err == nil {
				pkg.ReleaseDate = t.UnixMilli()
			}
		}

		pkg.Name = "Google Chrome"
		pkg.PkgFileData = []model.FileData{
			{
				FileName:    fileName,
				DownloadUrl: downloadUrl,
				Size:        fileSize,
			},
		}
		pkg.Application = common.CHROME
		pkg.OsVersion = request.OsVersion
		pkg.Os = osType
		pkg.Arch = arch
		pkg.Version = version
		pkg.LatestPackageUrl = downloadUrl
		pkg.SupportUrl = "https://www.google.com/chrome/"
		pkg.Publisher = "Google LLC"
		pkg.Uuid = uuid

		if modelPkg.Id > 0 {
			pkg.Id = modelPkg.Id
			pkg.UpdatedTime = time.Now().UnixMilli()
			_, err = NewThirdPartyPackageService().Repository.Update(&pkg)
			if err != nil {
				logger.ServiceLogger.Error("Error while updating chrome package for pkg : ", err)
			}
		} else {
			pkg.UpdatedTime = time.Now().UnixMilli()
			pkg.CreatedTime = time.Now().UnixMilli()
			_, err = NewThirdPartyPackageService().Repository.Create(&pkg)
			if err != nil {
				logger.ServiceLogger.Error("Error while creating chrome package for pkg : ", err)
			}
		}
		GenerateXmlForWindows(chromeMetaData, version, arch, pkg.Uuid, common.CHROME)
		return nil
	}

	return fmt.Errorf("update check status not OK")
}
