package thirdparty

import (
	"fmt"
	"os"
	"patch-central-repo/common"
	"patch-central-repo/logger"
	"patch-central-repo/model/thirdparty"
	"patch-central-repo/repository"
	"patch-central-repo/rest"
	service2 "patch-central-repo/service"
	"path/filepath"
	"strings"
	"time"
)

type ThirdPartyPackageService struct {
	Repository *repository.ThirdPartyPackageRepository
}

func NewThirdPartyPackageService() *ThirdPartyPackageService {
	return &ThirdPartyPackageService{
		Repository: repository.NewThirdPartyPackageRepository(),
	}
}

// Convert REST to Model
func (service *ThirdPartyPackageService) convertToModel(pkgRest thirdparty.ThirdPartyPackageRest) (*thirdparty.ThirdPartyPackage, error) {
	var osType common.OsType
	osType, _ = osType.ToOsType(pkgRest.Os)

	var osArch common.OsArchitecture
	osArch, _ = osArch.ToOsArch(pkgRest.Arch)

	var app common.ThirdPartyApplication
	app = app.ToThirdPartyApplication(pkgRest.Application)

	return &thirdparty.ThirdPartyPackage{
		BaseEntityModel:  service2.ConvertToBaseEntityModel(pkgRest.BaseEntityRest),
		Description:      pkgRest.Description,
		Version:          pkgRest.Version,
		Os:               osType,
		Arch:             osArch,
		LanguageCode:     pkgRest.LanguageCode,
		PkgFileData:      pkgRest.PkgFileData,
		LatestPackageUrl: pkgRest.LatestPackageUrl,
		Publisher:        pkgRest.Publisher,
		SupportUrl:       pkgRest.SupportUrl,
		ReleaseNote:      pkgRest.ReleaseNote,
		ReleaseDate:      pkgRest.ReleaseDate,
		Application:      app,
		CveDetails:       pkgRest.CveDetails,
		Uuid:             pkgRest.Uuid,
		UpdateId:         pkgRest.UpdateId,
	}, nil
}

// Convert Model to REST
func (service *ThirdPartyPackageService) convertToRest(pkg *thirdparty.ThirdPartyPackage) thirdparty.ThirdPartyPackageRest {
	return thirdparty.ThirdPartyPackageRest{
		BaseEntityRest:   service2.ConvertToBaseEntityRest(pkg.BaseEntityModel),
		Description:      pkg.Description,
		Version:          pkg.Version,
		Os:               pkg.Os.String(),
		Arch:             pkg.Arch.String(),
		LanguageCode:     pkg.LanguageCode,
		PkgFileData:      pkg.PkgFileData,
		LatestPackageUrl: pkg.LatestPackageUrl,
		Publisher:        pkg.Publisher,
		SupportUrl:       pkg.SupportUrl,
		ReleaseNote:      pkg.ReleaseNote,
		ReleaseDate:      pkg.ReleaseDate,
		Application:      pkg.Application.String(),
		CveDetails:       pkg.CveDetails,
		Uuid:             pkg.Uuid,
		UpdateId:         pkg.UpdateId,
	}
}

func (service ThirdPartyPackageService) Create(rest thirdparty.ThirdPartyPackageRest) (int64, common.CustomError) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to create package"))

	pkg, _ := service.convertToModel(rest)
	pkg.CreatedTime = time.Now().UnixMilli()
	pkg.UpdatedTime = time.Now().UnixMilli()
	id, err := service.Repository.Create(pkg)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while creating user ,Error : %s ", err.Error()))
		return 0, common.CustomError{Message: err.Error()}
	}
	return id, common.CustomError{}
}

func (service ThirdPartyPackageService) SyncThirdPartyPatchRepo() {
	logger.ServiceLogger.Debug("[SyncThirdPartyPatchRepo] : syncing third party patch repositories....")
	//NewFirefoxPatchPoolingService().ExecuteSync()
	//	NewAdobePatchPoolingService().ExecuteSync()
	NewChromePatchPoolingService().ExecuteSync()
	//	NewMicrosoftOfficeDataPoolingService().ExecuteSync()
	logger.ServiceLogger.Debug("[SyncThirdPartyPatchRepo] : third party patch repositories syncing completed....")
}

func DeleteXmlForWindows(uuid string, application common.ThirdPartyApplication) {
	filePath := filepath.Join(common.XMLDirectoryPath(), strings.ToLower(application.String()), uuid+".xml")
	_, err := os.Stat(filePath)
	if os.IsExist(err) {
		filePath = filepath.Join(filePath, uuid+".xml")
		err = os.Remove(filePath)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}
}

func GenerateXmlForWindows(data map[string]interface{}, version string, arch common.OsArchitecture, uuid string, application common.ThirdPartyApplication) {
	templateName := data["templateFileNameMap"].(map[string]interface{})[strings.ToLower(arch.String())]
	if templateName != nil {
		data, err := os.ReadFile(filepath.Join(common.ThirdPartyXMLDirectoryPath(), templateName.(string)))
		if err != nil {
			logger.ServiceLogger.Error("ThirdPartyPackageService.GenerateXmlForWindows error : ", err)
		}
		fileContent := string(data)
		if strings.Contains(fileContent, "{#version#}") {
			fileContent = strings.ReplaceAll(fileContent, "{#version#}", version)
		}
		if strings.Contains(fileContent, "{#uuid#}") {
			fileContent = strings.ReplaceAll(fileContent, "{#uuid#}", uuid)
		}

		filePath := filepath.Join(common.XMLDirectoryPath(), strings.ToLower(application.String()))
		_, err = os.Stat(filePath)
		if os.IsNotExist(err) {
			err := os.MkdirAll(filePath, 0755)
			if err != nil {
				logger.ServiceLogger.Error(err)
			}
		}

		filePath = filepath.Join(filePath, uuid+".xml")
		err = os.Remove(filePath)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
		xmlFile, err := os.Create(filePath)
		if err != nil {
			logger.ServiceLogger.Error("Error creating xml file ", filePath, " for uuid ", uuid)
		}
		defer func(xmlFile *os.File) {
			err := xmlFile.Close()
			if err != nil {
				logger.ServiceLogger.Error(err)
			}
		}(xmlFile)

		_, err = xmlFile.Write([]byte(fileContent))
		if err != nil {
			logger.ServiceLogger.Error("Error writing xml file ", filePath, " for uuid ", uuid)
		}
	}
}

func (service ThirdPartyPackageService) GetAllPackages(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	countQuery := rest.PrepareQueryFromSearchFilter(filter, common.ThirdPartyPackage.String(), true)
	var responsePage rest.ListResponseRest
	var packagePageList []thirdparty.ThirdPartyPackage
	var err error
	count := service.Repository.Count(countQuery)
	if count > 0 {
		searchQuery := rest.PrepareQueryFromSearchFilter(filter, common.ThirdPartyPackage.String(), false)
		packagePageList, err = service.Repository.GetAllPackage(searchQuery)
		if err != nil {
			return responsePage, err
		}
		responsePage.ObjectList = service.convertListToRest(packagePageList)
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}
	responsePage.TotalCount = count
	return responsePage, nil
}

func (service *ThirdPartyPackageService) convertListToRest(packList []thirdparty.ThirdPartyPackage) []thirdparty.ThirdPartyPackageRest {
	var packRestList []thirdparty.ThirdPartyPackageRest
	if len(packList) != 0 {
		for _, pack := range packList {
			packRest := service.convertToRest(&pack)
			packRestList = append(packRestList, packRest)
		}
	}
	return packRestList
}
